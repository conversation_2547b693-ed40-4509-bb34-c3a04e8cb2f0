-- 检查和修复用户状态问题
-- 执行时间：2025-08-03

-- 1. 首先查看当前用户状态分布
SELECT 
    username,
    status,
    CASE 
        WHEN status = 'ACTIVE' THEN '✓ 正确格式'
        WHEN LOWER(status) = 'active' THEN '需要修复为大写'
        WHEN status IS NULL THEN '状态为空'
        ELSE '未知状态格式'
    END as status_check
FROM user 
ORDER BY username;

-- 2. 修复hlh用户的状态（如果存在）
UPDATE user 
SET status = 'ACTIVE' 
WHERE username = 'hlh' AND (status IS NULL OR LOWER(status) = 'active' OR status != 'ACTIVE');

-- 3. 修复所有用户的状态格式
UPDATE user SET status = 'ACTIVE' WHERE LOWER(status) = 'active';
UPDATE user SET status = 'INACTIVE' WHERE LOWER(status) = 'inactive';
UPDATE user SET status = 'ACTIVE' WHERE status IS NULL OR status = '';

-- 4. 验证修复结果
SELECT 
    username,
    status,
    '修复后状态' as note
FROM user 
WHERE username = 'hlh'
ORDER BY username;

-- 5. 显示所有用户的最终状态
SELECT 
    status,
    COUNT(*) as user_count
FROM user 
GROUP BY status
ORDER BY status;
