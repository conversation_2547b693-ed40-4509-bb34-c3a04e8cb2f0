<template>
  <div class="report-dashboard" v-loading="loading">
    <div class="page-header">
      <h2>数据报表</h2>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateRangeChange"
        />
        <el-button type="primary" @click="exportReport">
          <el-icon><Download /></el-icon>导出报表
        </el-button>
      </div>
    </div>
    
    <!-- 报表概览 -->
    <el-row :gutter="20" class="overview-section">
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon sales">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">¥{{ reportData.totalSales.toFixed(2) }}</div>
              <div class="overview-label">总销售额</div>
              <div class="overview-change" :class="{ positive: reportData.salesGrowth >= 0 }">
                {{ reportData.salesGrowth >= 0 ? '+' : '' }}{{ reportData.salesGrowth.toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon purchase">
              <el-icon><ShoppingBag /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">¥{{ reportData.totalPurchase.toFixed(2) }}</div>
              <div class="overview-label">总采购额</div>
              <div class="overview-change" :class="{ positive: reportData.purchaseGrowth >= 0 }">
                {{ reportData.purchaseGrowth >= 0 ? '+' : '' }}{{ reportData.purchaseGrowth.toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon profit">
              <el-icon><Money /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">¥{{ reportData.totalProfit.toFixed(2) }}</div>
              <div class="overview-label">总利润</div>
              <div class="overview-change" :class="{ positive: reportData.profitGrowth >= 0 }">
                {{ reportData.profitGrowth >= 0 ? '+' : '' }}{{ reportData.profitGrowth.toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="overview-card">
          <div class="overview-content">
            <div class="overview-icon orders">
              <el-icon><Document /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-number">{{ reportData.totalOrders }}</div>
              <div class="overview-label">总订单数</div>
              <div class="overview-change" :class="{ positive: reportData.ordersGrowth >= 0 }">
                {{ reportData.ordersGrowth >= 0 ? '+' : '' }}{{ reportData.ordersGrowth.toFixed(1) }}%
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>销售趋势分析</span>
              <el-radio-group v-model="salesChartType" @change="updateSalesChart">
                <el-radio-button value="daily">日销售</el-radio-button>
                <el-radio-button value="monthly">月销售</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="salesTrendChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>药品销售排行</span>
          </template>
          <div ref="medicineRankChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="charts-section">
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>客户分布</span>
          </template>
          <div ref="customerDistributionChartRef" class="chart-container-small"></div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>供应商占比</span>
          </template>
          <div ref="supplierRatioChartRef" class="chart-container-small"></div>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card>
          <template #header>
            <span>库存状态</span>
          </template>
          <div ref="inventoryStatusChartRef" class="chart-container-small"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 详细报表 -->
    <el-row :gutter="20" class="table-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>热销药品排行</span>
          </template>
          <el-table :data="hotMedicines" stripe>
            <el-table-column prop="rank" label="排名" width="60" />
            <el-table-column prop="name" label="药品名称" />
            <el-table-column prop="sales" label="销售量" width="80" />
            <el-table-column prop="amount" label="销售额" width="100">
              <template #default="{ row }">
                ¥{{ row.amount.toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>库存预警</span>
          </template>
          <el-table :data="lowStockMedicines" stripe>
            <el-table-column prop="name" label="药品名称" />
            <el-table-column prop="currentStock" label="当前库存" width="80" />
            <el-table-column prop="minStock" label="最低库存" width="80" />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="getStockStatusType(row.status)">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Download,
  TrendCharts,
  ShoppingBag,
  Money,
  Document
} from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import {
  getReportOverview,
  getSalesTrend,
  getMedicineRank,
  getCustomerDistribution,
  getSupplierRatio,
  getInventoryStatus,
  getHotMedicines,
  getLowStock,
  type ReportOverview,
  type SalesTrendData,
  type ChartDataItem,
  type MedicineRankItem,
  type HotMedicineItem,
  type LowStockItem
} from '@/api/modules/report';

// 日期范围
const dateRange = ref<[Date, Date]>([
  new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
  new Date()
]);

// 报表数据
const reportData = reactive<ReportOverview>({
  totalSales: 0,
  salesGrowth: 0,
  totalPurchase: 0,
  purchaseGrowth: 0,
  totalProfit: 0,
  profitGrowth: 0,
  totalOrders: 0,
  ordersGrowth: 0
});

// 加载状态
const loading = ref(false);

// 图表相关
const salesTrendChartRef = ref<HTMLDivElement>();
const medicineRankChartRef = ref<HTMLDivElement>();
const customerDistributionChartRef = ref<HTMLDivElement>();
const supplierRatioChartRef = ref<HTMLDivElement>();
const inventoryStatusChartRef = ref<HTMLDivElement>();

const salesChartType = ref('daily');
let salesTrendChart: echarts.ECharts | null = null;
let medicineRankChart: echarts.ECharts | null = null;
let customerDistributionChart: echarts.ECharts | null = null;
let supplierRatioChart: echarts.ECharts | null = null;
let inventoryStatusChart: echarts.ECharts | null = null;

// 热销药品数据
const hotMedicines = ref<HotMedicineItem[]>([]);

// 库存预警数据
const lowStockMedicines = ref<LowStockItem[]>([]);

// 初始化销售趋势图表
const initSalesTrendChart = () => {
  if (!salesTrendChartRef.value) return;
  
  salesTrendChart = echarts.init(salesTrendChartRef.value);
  updateSalesChart();
};

// 更新销售图表
const updateSalesChart = async () => {
  if (!salesTrendChart) return;

  try {
    const startDate = dateRange.value[0] ? formatDate(dateRange.value[0]) : undefined;
    const endDate = dateRange.value[1] ? formatDate(dateRange.value[1]) : undefined;

    const response = await getSalesTrend({
      type: salesChartType.value,
      startDate,
      endDate
    });

    const trendData = response || {};

    // 提供默认数据以防API返回空数据
    const defaultData = {
      xAxisData: ['1日', '2日', '3日', '4日', '5日', '6日', '7日'],
      salesData: [2000, 2500, 3000, 2800, 3200, 2900, 3100],
      purchaseData: [1500, 1800, 2200, 2000, 2400, 2100, 2300]
    };

    const chartData = {
      xAxisData: trendData.xAxisData || defaultData.xAxisData,
      salesData: trendData.salesData || defaultData.salesData,
      purchaseData: trendData.purchaseData || defaultData.purchaseData
    };

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['销售额', '采购额']
      },
      xAxis: {
        type: 'category',
        data: chartData.xAxisData
      },
      yAxis: {
        type: 'value',
        name: '金额(元)'
      },
      series: [
        {
          name: '销售额',
          type: 'line',
          data: chartData.salesData,
          smooth: true,
          itemStyle: { color: '#409EFF' }
        },
        {
          name: '采购额',
          type: 'line',
          data: chartData.purchaseData,
          smooth: true,
          itemStyle: { color: '#67C23A' }
        }
      ]
    };

    salesTrendChart.setOption(option);
  } catch (error: any) {
    ElMessage.error('获取销售趋势数据失败');
    console.error('Sales trend error:', error);

    // 显示默认图表
    const defaultOption = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['销售额', '采购额'] },
      xAxis: { type: 'category', data: ['暂无数据'] },
      yAxis: { type: 'value', name: '金额(元)' },
      series: [
        { name: '销售额', type: 'line', data: [0], itemStyle: { color: '#409EFF' } },
        { name: '采购额', type: 'line', data: [0], itemStyle: { color: '#67C23A' } }
      ]
    };
    salesTrendChart.setOption(defaultOption);
  }
};

// 初始化药品排行图表
const initMedicineRankChart = async () => {
  if (!medicineRankChartRef.value) return;

  medicineRankChart = echarts.init(medicineRankChartRef.value);

  try {
    const startDate = dateRange.value[0] ? formatDate(dateRange.value[0]) : undefined;
    const endDate = dateRange.value[1] ? formatDate(dateRange.value[1]) : undefined;

    const response = await getMedicineRank({
      startDate,
      endDate,
      limit: 5
    });

    const rankData = response || [];

    // 提供默认数据以防API返回空数据
    const defaultData = [
      { name: '阿莫西林胶囊', sales: 156 },
      { name: '布洛芬片', sales: 142 },
      { name: '感冒灵颗粒', sales: 128 },
      { name: '维生素C片', sales: 115 },
      { name: '板蓝根颗粒', sales: 98 }
    ];

    const chartData = Array.isArray(rankData) && rankData.length > 0 ? rankData : defaultData;
    const names = chartData.map(item => item.name).reverse();
    const sales = chartData.map(item => item.sales).reverse();

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'value'
      },
      yAxis: {
        type: 'category',
        data: names
      },
      series: [{
        type: 'bar',
        data: sales,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }]
    };

    medicineRankChart.setOption(option);
  } catch (error: any) {
    ElMessage.error('获取药品排行数据失败');
    console.error('Medicine rank error:', error);

    // 显示默认图表
    const defaultOption = {
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      xAxis: { type: 'value' },
      yAxis: { type: 'category', data: ['暂无数据'] },
      series: [{
        type: 'bar',
        data: [0],
        itemStyle: { color: '#83bff6' }
      }]
    };
    medicineRankChart.setOption(defaultOption);
  }
};

// 初始化客户分布图表
const initCustomerDistributionChart = async () => {
  if (!customerDistributionChartRef.value) return;

  customerDistributionChart = echarts.init(customerDistributionChartRef.value);

  try {
    const response = await getCustomerDistribution();
    const distributionData = response || [];

    // 提供默认数据以防API返回空数据
    const defaultData = [
      { name: '个人客户', value: 35 },
      { name: '医院', value: 28 },
      { name: '药店', value: 22 },
      { name: '其他', value: 15 }
    ];

    const chartData = Array.isArray(distributionData) && distributionData.length > 0 ? distributionData : defaultData;

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: '客户分布',
        type: 'pie',
        radius: '70%',
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    customerDistributionChart.setOption(option);
  } catch (error: any) {
    ElMessage.error('获取客户分布数据失败');
    console.error('Customer distribution error:', error);

    // 显示默认图表
    const defaultOption = {
      tooltip: { trigger: 'item' },
      series: [{
        name: '客户分布',
        type: 'pie',
        radius: '70%',
        data: [{ name: '暂无数据', value: 100 }]
      }]
    };
    customerDistributionChart.setOption(defaultOption);
  }
};

// 初始化供应商占比图表
const initSupplierRatioChart = async () => {
  if (!supplierRatioChartRef.value) return;

  supplierRatioChart = echarts.init(supplierRatioChartRef.value);

  try {
    const response = await getSupplierRatio();
    const ratioData = response || [];

    // 提供默认数据以防API返回空数据
    const defaultData = [
      { name: '华润医药', value: 40 },
      { name: '国药控股', value: 30 },
      { name: '上海医药', value: 20 },
      { name: '其他', value: 10 }
    ];

    const chartData = Array.isArray(ratioData) && ratioData.length > 0 ? ratioData : defaultData;

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: '供应商占比',
        type: 'pie',
        radius: ['40%', '70%'],
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    supplierRatioChart.setOption(option);
  } catch (error: any) {
    ElMessage.error('获取供应商占比数据失败');
    console.error('Supplier ratio error:', error);

    // 显示默认图表
    const defaultOption = {
      tooltip: { trigger: 'item' },
      series: [{
        name: '供应商占比',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [{ name: '暂无数据', value: 100 }]
      }]
    };
    supplierRatioChart.setOption(defaultOption);
  }
};

// 初始化库存状态图表
const initInventoryStatusChart = async () => {
  if (!inventoryStatusChartRef.value) return;

  inventoryStatusChart = echarts.init(inventoryStatusChartRef.value);

  try {
    const response = await getInventoryStatus();
    const statusData = response || [];

    // 提供默认数据以防API返回空数据
    const defaultData = [
      { name: '库存充足', value: 60 },
      { name: '库存不足', value: 25 },
      { name: '严重不足', value: 15 }
    ];

    const chartData = Array.isArray(statusData) && statusData.length > 0 ? statusData : defaultData;

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: '库存状态',
        type: 'pie',
        radius: '70%',
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    inventoryStatusChart.setOption(option);
  } catch (error: any) {
    ElMessage.error('获取库存状态数据失败');
    console.error('Inventory status error:', error);

    // 显示默认图表
    const defaultOption = {
      tooltip: { trigger: 'item' },
      series: [{
        name: '库存状态',
        type: 'pie',
        radius: '70%',
        data: [{ name: '暂无数据', value: 100 }]
      }]
    };
    inventoryStatusChart.setOption(defaultOption);
  }
};

// 处理日期范围变化
const handleDateRangeChange = (dates: [Date, Date] | null) => {
  if (dates) {
    // 根据日期范围重新获取数据
    fetchReportData();
  }
};

// 获取报表数据
const fetchReportData = async () => {
  if (loading.value) return;

  loading.value = true;
  try {
    const startDate = dateRange.value[0] ? formatDate(dateRange.value[0]) : undefined;
    const endDate = dateRange.value[1] ? formatDate(dateRange.value[1]) : undefined;

    // 并行获取所有数据
    const [
      overviewResponse,
      hotMedicinesResponse,
      lowStockResponse
    ] = await Promise.all([
      getReportOverview({ startDate, endDate }),
      getHotMedicines({ startDate, endDate, limit: 5 }),
      getLowStock()
    ]);

    // 更新概览数据
    console.log('Overview response:', overviewResponse);
    console.log('Overview data:', overviewResponse?.data);

    if (overviewResponse && overviewResponse.data) {
      Object.assign(reportData, overviewResponse.data);
      console.log('Updated reportData:', reportData);
    } else {
      console.error('No overview data received');
    }

    // 更新表格数据
    hotMedicines.value = hotMedicinesResponse || [];
    lowStockMedicines.value = lowStockResponse || [];

    // 更新图表
    await updateSalesChart();
    await initMedicineRankChart();
    await initCustomerDistributionChart();
    await initSupplierRatioChart();
    await initInventoryStatusChart();

    ElMessage.success('报表数据已更新');
  } catch (error: any) {
    ElMessage.error(error.msg || '获取报表数据失败');
    console.error('Fetch report data error:', error);
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 导出报表
const exportReport = () => {
  ElMessage.success('报表导出功能开发中...');
};

// 获取库存状态类型
const getStockStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '严重不足': 'danger',
    '库存不足': 'warning',
    '库存正常': 'success'
  };
  return statusMap[status] || 'info';
};

// 页面加载时初始化
onMounted(() => {
  nextTick(async () => {
    // 初始化销售趋势图表
    initSalesTrendChart();

    // 获取所有报表数据
    await fetchReportData();

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      salesTrendChart?.resize();
      medicineRankChart?.resize();
      customerDistributionChart?.resize();
      supplierRatioChart?.resize();
      inventoryStatusChart?.resize();
    });
  });
});
</script>

<style scoped>
.report-dashboard {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.overview-section {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.overview-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 20px;
}

.overview-icon.sales {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-icon.purchase {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-icon.profit {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-icon.orders {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.overview-info {
  flex: 1;
}

.overview-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.overview-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.overview-change {
  font-size: 12px;
  color: #f56c6c;
}

.overview-change.positive {
  color: #67c23a;
}

.charts-section {
  margin-bottom: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 350px;
}

.chart-container-small {
  height: 250px;
}
</style>